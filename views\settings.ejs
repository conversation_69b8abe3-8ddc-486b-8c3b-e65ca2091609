<div class="card" style="max-width: 600px; margin: 0 auto;">
  <div class="card-header">
    <h2 class="card-title">Settings</h2>
  </div>
  
  <!-- Environment Settings -->
  <div class="form-group">
    <label for="scheduler-cron" class="form-label">Scheduler C<PERSON> Pattern</label>
    <input 
      type="text" 
      id="scheduler-cron" 
      value="<%= env.SCHEDULER_CRON %>" 
      class="form-input" 
      readonly
    >
    <small class="text-secondary">
      Adjust this setting in your <code>.env</code> file to change scheduler timing.
    </small>
  </div>

  <div class="form-group">
    <label for="max-daily-generations" class="form-label">Max Generations per Day</label>
    <input 
      type="text" 
      id="max-daily-generations" 
      value="<%= env.MAX_GENERATIONS_PER_DAY %>" 
      class="form-input" 
      readonly
    >
    <small class="text-secondary">
      Adjust this setting in your <code>.env</code> file to change daily limits.
    </small>
  </div>

  <div class="form-group">
    <label for="image-size" class="form-label">Image Size</label>
    <input 
      type="text" 
      id="image-size" 
      value="<%= env.IMAGE_SIZE %>" 
      class="form-input" 
      readonly
    >
    <small class="text-secondary">
      Current setting for generated image dimensions.
    </small>
  </div>

  <div class="form-group">
    <label for="image-quality" class="form-label">Image Quality</label>
    <input 
      type="text" 
      id="image-quality" 
      value="<%= env.IMAGE_QUALITY %>" 
      class="form-input" 
      readonly
    >
    <small class="text-secondary">
      Current quality setting for generated images.
    </small>
  </div>

  <div class="form-group">
    <label for="image-format" class="form-label">Image Format</label>
    <input 
      type="text" 
      id="image-format" 
      value="<%= env.IMAGE_FORMAT %>" 
      class="form-input" 
      readonly
    >
    <small class="text-secondary">
      Current format setting for generated images.
    </small>
  </div>

  <!-- Scheduler Control -->
  <div class="form-group">
    <div class="form-checkbox">
      <input 
        type="checkbox" 
        id="scheduler-enabled" 
        <%= env.SCHEDULER_ENABLED === 'true' ? 'checked' : '' %>
        disabled
      >
      <label for="scheduler-enabled" class="form-label mb-0">Enable Scheduler</label>
    </div>
    <small class="text-secondary">
      Adjust this setting in your <code>.env</code> file to enable or disable scheduler.
    </small>
  </div>

  <div class="form-group flex gap-2">
    <a href="/" class="btn btn-outline">
      Back to Dashboard
    </a>
  </div>
</div>

<script>
// Additional client-side functionality could be added here
</script>
