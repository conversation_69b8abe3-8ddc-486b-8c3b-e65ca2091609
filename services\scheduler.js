const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const openaiService = require('./openai');
const cloudinaryService = require('./cloudinary');

class SchedulerService {
  constructor() {
    this.dataPath = path.join(__dirname, '..', 'data', 'prompts.json');
  }

  async readData() {
    try {
      const data = await fs.readFile(this.dataPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error reading scheduler data:', error);
      return {
        prompts: [],
        generationHistory: [],
        settings: {
          currentIndex: 0,
          lastGeneration: null,
          totalGenerations: 0,
          dailyGenerations: 0,
          lastResetDate: null
        }
      };
    }
  }

  async writeData(data) {
    await fs.writeFile(this.dataPath, JSON.stringify(data, null, 2));
  }

  async processNextPrompt() {
    try {
      const data = await this.readData();
      
      // Check daily generation limit
      const maxDaily = parseInt(process.env.MAX_GENERATIONS_PER_DAY) || 10;
      const today = new Date().toDateString();
      const lastResetDate = data.settings.lastResetDate;
      
      // Reset daily count if it's a new day
      if (!lastResetDate || new Date(lastResetDate).toDateString() !== today) {
        data.settings.dailyGenerations = 0;
        data.settings.lastResetDate = today;
      }
      
      if (data.settings.dailyGenerations >= maxDaily) {
        console.log(`⏸️ Daily generation limit reached (${maxDaily}). Skipping...`);
        return { success: false, reason: 'Daily limit reached' };
      }

      // Get enabled prompts
      const enabledPrompts = data.prompts.filter(p => p.enabled);
      
      if (enabledPrompts.length === 0) {
        console.log('⏸️ No enabled prompts found. Skipping generation...');
        return { success: false, reason: 'No enabled prompts' };
      }

      // Get next prompt (sequential)
      let currentIndex = data.settings.currentIndex || 0;
      if (currentIndex >= enabledPrompts.length) {
        currentIndex = 0; // Reset to beginning
      }

      const selectedPrompt = enabledPrompts[currentIndex];
      console.log(`🎯 Processing prompt ${currentIndex + 1}/${enabledPrompts.length}: ${selectedPrompt.text}`);

      // Generate image
      const result = await openaiService.generateImageFromPromptData(selectedPrompt);
      
      if (result.success) {
        // Upload to Cloudinary
        const cloudinaryResult = await cloudinaryService.uploadImage(result.filepath, {
          prompt: selectedPrompt.text,
          referenceImage: selectedPrompt.referenceImage
        });
        
        // Create generation record
        const generationRecord = {
          id: uuidv4(),
          promptId: selectedPrompt.id,
          promptText: selectedPrompt.text,
          generatedImage: result.filename,
          cloudinaryUrl: cloudinaryResult.url,
          cloudinaryPublicId: cloudinaryResult.public_id,
          generatedAt: new Date().toISOString(),
          usage: result.usage,
          manual: false,
          scheduledGeneration: true
        };
        
        // Update data
        data.generationHistory.push(generationRecord);
        
        // Update prompt statistics
        const promptIndex = data.prompts.findIndex(p => p.id === selectedPrompt.id);
        if (promptIndex !== -1) {
          data.prompts[promptIndex].lastUsed = new Date().toISOString();
          data.prompts[promptIndex].timesUsed += 1;
        }
        
        // Update settings
        data.settings.currentIndex = (currentIndex + 1) % enabledPrompts.length;
        data.settings.totalGenerations += 1;
        data.settings.dailyGenerations += 1;
        data.settings.lastGeneration = new Date().toISOString();
        
        await this.writeData(data);
        
        console.log('✅ Scheduled generation completed successfully');
        console.log(`📊 Stats: Total: ${data.settings.totalGenerations}, Today: ${data.settings.dailyGenerations}/${maxDaily}`);
        
        return {
          success: true,
          prompt: selectedPrompt,
          generatedImage: result.filename,
          cloudinaryUrl: cloudinaryResult.url,
          usage: result.usage
        };
      } else {
        console.error('❌ Image generation failed');
        return { success: false, reason: 'Generation failed' };
      }
    } catch (error) {
      console.error('❌ Scheduler processing error:', error);
      throw error;
    }
  }

  async getNextPrompt() {
    try {
      const data = await this.readData();
      const enabledPrompts = data.prompts.filter(p => p.enabled);
      
      if (enabledPrompts.length === 0) {
        return null;
      }

      let currentIndex = data.settings.currentIndex || 0;
      if (currentIndex >= enabledPrompts.length) {
        currentIndex = 0;
      }

      return enabledPrompts[currentIndex];
    } catch (error) {
      console.error('Error getting next prompt:', error);
      return null;
    }
  }

  async getSchedulerStats() {
    try {
      const data = await this.readData();
      const enabledPrompts = data.prompts.filter(p => p.enabled);
      const today = new Date().toDateString();
      const todayGenerations = data.generationHistory.filter(
        g => new Date(g.generatedAt).toDateString() === today
      ).length;

      return {
        totalPrompts: data.prompts.length,
        enabledPrompts: enabledPrompts.length,
        totalGenerations: data.settings.totalGenerations,
        todayGenerations: todayGenerations,
        lastGeneration: data.settings.lastGeneration,
        nextPrompt: await this.getNextPrompt(),
        dailyLimit: parseInt(process.env.MAX_GENERATIONS_PER_DAY) || 10,
        schedulerEnabled: process.env.SCHEDULER_ENABLED === 'true'
      };
    } catch (error) {
      console.error('Error getting scheduler stats:', error);
      return null;
    }
  }

  async resetDailyCount() {
    try {
      const data = await this.readData();
      data.settings.dailyGenerations = 0;
      data.settings.lastResetDate = new Date().toDateString();
      await this.writeData(data);
      console.log('✅ Daily generation count reset');
    } catch (error) {
      console.error('Error resetting daily count:', error);
      throw error;
    }
  }
}

module.exports = new SchedulerService();
