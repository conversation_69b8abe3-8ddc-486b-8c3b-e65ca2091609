## 🧠 What Is gpt‑image‑1?

* A state‑of‑the‑art multimodal image generation model released by OpenAI on April 23, 2025 ([OpenAI][1]).
* It accepts **text** and/or **image** inputs and produces **high‑quality image outputs** with options for resolution, quality, format, and transparency .
* Includes safety guardrails and C2PA metadata; moderation can be set to `auto` or `low` ([OpenAI][1]).


---

## 🔧 API Endpoints & Parameters

### 1. **Generate Image**

**HTTP POST** to `https://api.openai.com/v1/images/generations`
**Payload Parameters**:

* `model`: `"gpt-image-1"`
* `prompt`: *string* — Text describing the image
* `n`: *integer* — Number of images (default 1, max 10)
* `size`: `["1024x1024","1024x1536","1536x1024","auto"]`
* `quality`: `["low","medium","high","auto"]`
* `background`: `["opaque","transparent","auto"]`
* `moderation`: `["auto","low"]`
* `output_format`: `["png","jpeg","webp"]`
* `output_compression`: *int* 1–100
* Optional: image inputs or masks for editing/inpainting ([OpenAI][1], [LaoZhang AI][5], [docs.aihubmix.com][3], [Segmind][6], [Analytics Vidhya][2])

**Example (Python SDK)**:

```python
from openai import OpenAI, openai

client = OpenAI(api_key="YOUR_KEY")

resp = client.images.generate(
  model="gpt-image-1",
  prompt="A serene park scene with humans and robots sharing a bench",
  size="1024x1024",
  quality="medium",
  background="auto",
  moderation="auto",
)
# Base64 image returned in resp.data[0].b64_json
```



**Example (cURL)**:

```bash
curl -X POST "https://api.openai.com/v1/images/generations" \
-H "Authorization: Bearer $API_KEY" \
-H "Content-Type: application/json" \
-d '{
  "model": "gpt-image-1",
  "prompt": "Futuristic city skyline at sunset",
  "n": 1,
  "size": "1024x1024",
  "quality": "high",
  "moderation": "auto",
  "background": "opaque",
  "output_format": "png"
}'
```



---

### 2. **Edit Image / Inpainting**

Same endpoint but include:

* `image`: uploaded file
* `mask`: PNG mask (transparent areas are editable)
* `prompt`: instruction for edit ([Analytics Vidhya][2])

```python
resp = client.images.edit(
  model="gpt-image-1",
  image=open("scene.png","rb"),
  mask=open("mask.png","rb"),
  prompt="Add a smiling cat to the empty park bench"
)
```

---

## 📥 Response Format

```json
{
  "data": [
    {
      "b64_json": "base64-encoded-image"
      // or "url": temporary URL (expires in ~60 minutes)
    }
  ],
  "usage": {
    "input_tokens_details": {
      "text_tokens": 10,
      "image_tokens": 200
    },
    "output_tokens": 300
  }
}
```

Use the `usage` field to track costs ([docs.aihubmix.com][3]).

---

## 🛠 Integration Tips

* Ensure your organization is **verified** to access the model ([OpenAI][1]).
* Download or cache images promptly since URLs expire after 60 minutes ([OpenAI Help Center][7]).
* Choose resolutions ("size") based on project needs: square, portrait, or landscape ([docs.aihubmix.com][3]).
* Use `moderation=low` if default moderation is too restrictive ([OpenAI][1]).
* To composite elements, upload a base image + mask and instruct the model via prompt.

---

## ✅ Use Case Walkthrough

1. **Generate a brand-new image**:

   * Send a text-only prompt to `images.generate`.
   * Retrieve base64 from `resp.data[0]`, save as PNG/JPEG.
2. **Edit an existing image**:

   * Upload original image and a PNG mask (transparent where changes are desired).
   * Prompt: `"Add X to the transparent region"`.
3. **Generate multiple variations**:

   * Set `n=3` to retrieve three image options.

---

## 🚀 Quickstart Checklist

| Step                                                       | Completed? |
| ---------------------------------------------------------- | :--------: |
| ✅ Get API key & verify org                                 |            |
| ✅ Install SDK: `pip install openai` / `npm install openai` |            |
| ✅ Choose endpoint & parameters                             |            |
| ✅ Make your first request                                  |            |
| ✅ Base64‑decode & save output                              |            |
| ✅ Track usage and optimize costs                           |            |

---
