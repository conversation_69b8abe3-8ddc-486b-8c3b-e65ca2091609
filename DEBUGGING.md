# Debugging and Batch Processing Guide

This document explains the comprehensive debugging and batch processing features implemented in the AI Image Generator.

## 🔍 Debugging Features

### Enhanced Logging System

The application now includes a sophisticated debugging system with multiple levels and categories:

#### Debug Levels
- `NONE` (0): No logging
- `ERROR` (1): Only errors
- `WARN` (2): Warnings and errors
- `INFO` (3): General information, warnings, and errors
- `DEBUG` (4): Detailed debugging information
- `VERBOSE` (5): Maximum detail including API requests/responses

#### Debug Categories
- `GENERAL`: General application logging
- `IMAGE_GENERATION`: Image generation process
- `BATCH_PROCESSING`: Batch processing operations
- `CLOUDINARY`: Cloudinary upload operations
- `SCHEDULER`: Scheduled generation tasks
- `API`: API requests and responses
- `FILE_OPERATIONS`: File system operations

### Environment Variables for Debugging

Configure debugging levels using environment variables:

```bash
# Global debug level
DEBUG_LEVEL=DEBUG

# Category-specific levels
DEBUG_IMAGE_GEN=VERBOSE
DEBUG_BATCH=DEBUG
DEBUG_CLOUDINARY=INFO
DEBUG_SCHEDULER=DEBUG
DEBUG_API=INFO
DEBUG_FILES=WARN

# Debug features
DEBUG_TIMESTAMPS=true
DEBUG_SHOW_CATEGORY=true
DEBUG_COLORS=true
DEBUG_LOG_FILE=false
DEBUG_LOG_PATH=./logs/debug.log
```

## 🚀 Batch Processing Features

### Comprehensive Group Processing

When generating images from a JSON group, the system now provides:

1. **Detailed Progress Tracking**: Real-time progress for each prompt in the group
2. **Error Handling**: Individual prompt failures don't stop the entire batch
3. **Retry Mechanism**: Failed prompts are automatically retried
4. **Skip Disabled Prompts**: Disabled prompts are automatically skipped
5. **Rate Limiting**: Automatic delays between generations to avoid API limits

### Batch Processing Statistics

Each batch operation provides detailed statistics:
- Total prompts processed
- Successful generations
- Failed generations
- Skipped prompts (disabled)
- Processing duration
- Success rate percentage

### Example Batch Output

```
🚀 Manual generation triggered for prompt group: My Test Group
📊 Group contains 5 prompts

🎯 Processing prompt 1/5:
   Text: "A beautiful sunset over mountains..."
   Enabled: true
✅ Prompt 1 generated successfully in 3.45s
   Image: generated_abc123.png
   Usage: {"total_tokens":4290,"input_tokens":130,"output_tokens":4160}
☁️ Prompt 1 uploaded to Cloudinary: https://res.cloudinary.com/...

⏳ Waiting 2 seconds before next generation...

🎯 Processing prompt 2/5:
   Text: "A futuristic cityscape..."
   Enabled: true
✅ Prompt 2 generated successfully in 2.87s
   Image: generated_def456.png

📊 Group generation completed:
   ✅ Successful: 4
   ❌ Failed: 1
   📝 Total processed: 5
```

## 🛠️ Testing the System

### Running the Test Script

A comprehensive test script is included to verify all functionality:

```bash
node test-batch-processing.js
```

This script:
1. Creates a test JSON file with sample prompts
2. Tests the batch processor with mock functions
3. Demonstrates debugging output
4. Provides next steps for manual testing

### Manual Testing Steps

1. **Start the server**:
   ```bash
   npm start
   ```

2. **Upload the test JSON file**:
   - Go to http://localhost:3000
   - Click "Add Prompt" → "Bulk Import"
   - Upload the generated `test-prompts.json` file

3. **Generate images from the group**:
   - Click the "Generate Group" button
   - Watch the console for detailed debugging output

4. **Monitor batch status**:
   - Check `/batch-status` endpoint for active batch information
   - Use `/batch-status/:batchId` for specific batch details

## 📊 Debugging Output Examples

### Image Generation Process

```
[2025-07-12T19:30:15.123Z] [IMAGE_GENERATION] DEBUG: 🚀 Starting image generation...
{
  "promptLength": 156,
  "referenceImage": "none",
  "model": "gpt-image-1",
  "size": "1024x1024",
  "quality": "medium"
}

[2025-07-12T19:30:15.124Z] [IMAGE_GENERATION] DEBUG: 📝 Using text-only generation
[2025-07-12T19:30:15.125Z] [IMAGE_GENERATION] VERBOSE: Request body
{
  "model": "gpt-image-1",
  "prompt": "A beautiful sunset...",
  "n": 1,
  "size": "1024x1024",
  "quality": "medium",
  "moderation": "auto",
  "output_format": "png"
}

[2025-07-12T19:30:18.567Z] [IMAGE_GENERATION] DEBUG: 📡 Received response from OpenAI API
[2025-07-12T19:30:18.568Z] [IMAGE_GENERATION] DEBUG: 💾 Saving image
{
  "filename": "generated_abc123.png",
  "filepath": "/path/to/generated-images/generated_abc123.png",
  "imageDataSize": 87654
}

[2025-07-12T19:30:18.789Z] [IMAGE_GENERATION] DEBUG: ✅ Image generated and saved successfully
{
  "finalFileSize": 65432,
  "filename": "generated_abc123.png"
}
```

### Batch Processing Output

```
[2025-07-12T19:30:20.000Z] [BATCH_PROCESSING] DEBUG: 🚀 Starting batch process: Group: My Test Group
{
  "batchId": "abc123-def456-ghi789",
  "totalItems": 5,
  "delayBetweenItems": 2000,
  "maxRetries": 1,
  "continueOnError": true
}

[2025-07-12T19:30:25.000Z] [BATCH_PROCESSING] INFO: Processing Statistics:
{
  "total": 5,
  "successful": 4,
  "failed": 1,
  "skipped": 0,
  "duration": "45.67s",
  "successRate": "80.0%"
}
```

## 🔧 Configuration Options

### Batch Processing Options

```javascript
const batchOptions = {
  batchName: 'Custom Batch Name',
  delayBetweenItems: 2000,      // 2 seconds between items
  maxRetries: 1,                // Retry failed items once
  continueOnError: true,        // Don't stop on individual failures
  logProgress: true             // Show detailed progress logs
};
```

### Debug Logger Configuration

```javascript
const debugConfig = {
  globalLevel: 'DEBUG',
  categoryLevels: {
    IMAGE_GENERATION: 'VERBOSE',
    BATCH_PROCESSING: 'DEBUG'
  },
  features: {
    showTimestamps: true,
    showCategory: true,
    colorOutput: true,
    logToFile: false
  }
};
```

## 🚨 Error Handling

### Comprehensive Error Tracking

The system now tracks and reports:
- Individual prompt failures within groups
- API rate limiting issues
- File system errors
- Cloudinary upload failures
- Network connectivity problems

### Error Recovery

- **Automatic Retries**: Failed generations are automatically retried
- **Graceful Degradation**: Cloudinary upload failures don't stop image generation
- **Partial Success**: Groups can succeed even if some prompts fail
- **Detailed Error Reporting**: Each error includes context and troubleshooting information

## 📈 Performance Monitoring

### Built-in Metrics

The system tracks:
- Generation time per image
- Batch processing duration
- Success/failure rates
- Token usage statistics
- File sizes and processing times

### Batch Status API

Monitor active batches via REST API:
- `GET /batch-status` - List all active batches
- `GET /batch-status/:batchId` - Get specific batch details

## 🎯 Best Practices

1. **Enable appropriate debug levels** for your environment
2. **Monitor batch processing** for large groups
3. **Check error logs** regularly for patterns
4. **Use test scripts** before deploying changes
5. **Configure rate limiting** appropriately for your API limits

## 🔄 Troubleshooting

### Common Issues

1. **All images fail to generate**:
   - Check API credentials
   - Verify network connectivity
   - Check debug logs for specific errors

2. **Some images in group fail**:
   - Check individual prompt content
   - Verify enabled/disabled status
   - Review error details in batch results

3. **Slow batch processing**:
   - Adjust `delayBetweenItems` setting
   - Check API rate limits
   - Monitor system resources

### Debug Commands

```bash
# Enable verbose debugging
DEBUG_LEVEL=VERBOSE npm start

# Test batch processing
node test-batch-processing.js

# Check batch status
curl http://localhost:3000/batch-status
```
