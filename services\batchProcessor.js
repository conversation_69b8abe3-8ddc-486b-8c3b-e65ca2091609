const { v4: uuidv4 } = require('uuid');
const ErrorHandler = require('../utils/errorHandler');

class BatchProcessor {
  constructor() {
    this.activeProcesses = new Map();
  }

  /**
   * Process a batch of prompts with detailed logging and error handling
   * @param {Array} prompts - Array of prompt objects to process
   * @param {Function} processingFunction - Function to process each prompt
   * @param {Object} options - Processing options
   * @returns {Object} - Batch processing results
   */
  async processBatch(prompts, processingFunction, options = {}) {
    const batchId = uuidv4();
    const {
      batchName = 'Batch Process',
      delayBetweenItems = 2000,
      maxRetries = 1,
      continueOnError = true,
      logProgress = true
    } = options;

    console.log(`🚀 Starting batch process: ${batchName}`);
    console.log(`   Batch ID: ${batchId}`);
    console.log(`   Total items: ${prompts.length}`);
    console.log(`   Delay between items: ${delayBetweenItems}ms`);
    console.log(`   Max retries per item: ${maxRetries}`);
    console.log(`   Continue on error: ${continueOnError}`);

    const results = [];
    const errors = [];
    let successCount = 0;
    let failureCount = 0;
    let skippedCount = 0;

    // Store batch info for tracking
    this.activeProcesses.set(batchId, {
      batchName,
      totalItems: prompts.length,
      processedItems: 0,
      successCount: 0,
      failureCount: 0,
      startTime: Date.now(),
      status: 'running'
    });

    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i];
      const itemIndex = i + 1;
      
      if (logProgress) {
        console.log(`\n📋 Processing item ${itemIndex}/${prompts.length}:`);
        console.log(`   Batch: ${batchName}`);
        console.log(`   Item: ${prompt.text ? prompt.text.substring(0, 100) + (prompt.text.length > 100 ? '...' : '') : 'No text'}`);
        console.log(`   Enabled: ${prompt.enabled !== false}`);
      }

      // Skip disabled items
      if (prompt.enabled === false) {
        if (logProgress) {
          console.log(`⏭️ Skipping disabled item ${itemIndex}`);
        }
        skippedCount++;
        continue;
      }

      let attempts = 0;
      let success = false;
      let lastError = null;

      // Retry logic
      while (attempts <= maxRetries && !success) {
        attempts++;
        
        try {
          if (attempts > 1 && logProgress) {
            console.log(`🔄 Retry attempt ${attempts}/${maxRetries + 1} for item ${itemIndex}`);
          }

          const startTime = Date.now();
          const result = await processingFunction(prompt, itemIndex, batchId);
          const endTime = Date.now();
          const duration = ((endTime - startTime) / 1000).toFixed(2);

          if (result && result.success) {
            if (logProgress) {
              console.log(`✅ Item ${itemIndex} processed successfully in ${duration}s`);
              if (result.filename) {
                console.log(`   Generated: ${result.filename}`);
              }
              if (result.usage) {
                console.log(`   Usage: ${JSON.stringify(result.usage)}`);
              }
            }

            results.push({
              index: itemIndex,
              success: true,
              result: result,
              duration: duration,
              attempts: attempts
            });
            
            successCount++;
            success = true;
          } else {
            throw new Error(result?.error || 'Processing failed - no success flag');
          }

        } catch (error) {
          lastError = error;
          
          if (logProgress) {
            console.error(`❌ Item ${itemIndex} failed (attempt ${attempts}/${maxRetries + 1}):`, error.message);
          }

          if (attempts <= maxRetries) {
            // Wait before retry
            if (logProgress) {
              console.log(`⏳ Waiting 5 seconds before retry...`);
            }
            await new Promise(resolve => setTimeout(resolve, 5000));
          }
        }
      }

      // If all attempts failed
      if (!success) {
        failureCount++;
        errors.push({
          index: itemIndex,
          prompt: prompt.text ? prompt.text.substring(0, 100) : 'No text',
          error: lastError?.message || 'Unknown error',
          attempts: attempts
        });

        if (!continueOnError) {
          console.error(`🛑 Stopping batch process due to error on item ${itemIndex}`);
          break;
        }
      }

      // Update batch tracking
      const batchInfo = this.activeProcesses.get(batchId);
      if (batchInfo) {
        batchInfo.processedItems = itemIndex;
        batchInfo.successCount = successCount;
        batchInfo.failureCount = failureCount;
      }

      // Add delay between items (except for the last item)
      if (i < prompts.length - 1 && delayBetweenItems > 0) {
        if (logProgress) {
          console.log(`⏳ Waiting ${delayBetweenItems / 1000}s before next item...`);
        }
        await new Promise(resolve => setTimeout(resolve, delayBetweenItems));
      }
    }

    // Final batch statistics
    const batchInfo = this.activeProcesses.get(batchId);
    const totalDuration = batchInfo ? ((Date.now() - batchInfo.startTime) / 1000).toFixed(2) : 'unknown';
    
    if (batchInfo) {
      batchInfo.status = 'completed';
      batchInfo.endTime = Date.now();
    }

    console.log(`\n📊 Batch process completed: ${batchName}`);
    console.log(`   Batch ID: ${batchId}`);
    console.log(`   Total duration: ${totalDuration}s`);
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ❌ Failed: ${failureCount}`);
    console.log(`   ⏭️ Skipped: ${skippedCount}`);
    console.log(`   📝 Total processed: ${successCount + failureCount}`);

    if (errors.length > 0) {
      console.log(`\n❌ Errors encountered:`);
      errors.forEach(error => {
        console.log(`   - Item ${error.index}: ${error.error} (${error.attempts} attempts)`);
      });
    }

    // Clean up tracking after some time
    setTimeout(() => {
      this.activeProcesses.delete(batchId);
    }, 300000); // 5 minutes

    return {
      batchId,
      batchName,
      success: successCount > 0,
      results,
      errors,
      stats: {
        total: prompts.length,
        processed: successCount + failureCount,
        successful: successCount,
        failed: failureCount,
        skipped: skippedCount,
        duration: totalDuration
      }
    };
  }

  /**
   * Get information about active batch processes
   * @returns {Array} - Array of active batch processes
   */
  getActiveBatches() {
    return Array.from(this.activeProcesses.entries()).map(([id, info]) => ({
      id,
      ...info
    }));
  }

  /**
   * Get information about a specific batch
   * @param {string} batchId - The batch ID to get info for
   * @returns {Object|null} - Batch information or null if not found
   */
  getBatchInfo(batchId) {
    return this.activeProcesses.get(batchId) || null;
  }
}

module.exports = new BatchProcessor();
