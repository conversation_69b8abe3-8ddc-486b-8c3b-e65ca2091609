/**
 * Error handling utilities for the AI Image Generator
 * Provides better error messages and troubleshooting guidance
 */

const { debug<PERSON>ogger, DEBUG_CATEGORIES } = require('../config/debug');

class ErrorHandler {
  /**
   * Parse and enhance OpenAI API errors with helpful context
   * @param {Error} error - The original error
   * @param {Object} context - Additional context about the operation
   * @returns {Object} Enhanced error information
   */
  static parseOpenAIError(error, context = {}) {
    const errorInfo = {
      type: 'unknown',
      message: error.message,
      originalError: error,
      context: context,
      troubleshooting: [],
      canRetry: false
    };

    // Check for billing limit errors
    if (error.message && error.message.includes('Billing hard limit')) {
      errorInfo.type = 'billing_limit';
      errorInfo.message = 'OpenAI billing limit reached';
      errorInfo.troubleshooting = [
        'Check your OpenAI account billing settings at https://platform.openai.com/account/billing',
        'Verify your payment method is valid and up to date',
        'Consider upgrading your billing plan if needed',
        'Check your usage limits and current balance'
      ];
      errorInfo.canRetry = false;
    }
    // Check for rate limit errors
    else if (error.message && (error.message.includes('rate limit') || error.message.includes('429'))) {
      errorInfo.type = 'rate_limit';
      errorInfo.message = 'OpenAI API rate limit exceeded';
      errorInfo.troubleshooting = [
        'Wait a few minutes before retrying',
        'Consider reducing the batch size',
        'Increase delays between API calls',
        'Check your API usage tier at https://platform.openai.com/account/limits'
      ];
      errorInfo.canRetry = true;
    }
    // Check for authentication errors
    else if (error.message && (error.message.includes('401') || error.message.includes('authentication'))) {
      errorInfo.type = 'authentication';
      errorInfo.message = 'OpenAI API authentication failed';
      errorInfo.troubleshooting = [
        'Verify your OPENAI_API_KEY is correct in your .env file',
        'Check that your API key is active at https://platform.openai.com/api-keys',
        'Ensure your API key has the necessary permissions',
        'Try regenerating your API key if needed'
      ];
      errorInfo.canRetry = false;
    }
    // Check for quota/usage errors
    else if (error.message && (error.message.includes('quota') || error.message.includes('insufficient'))) {
      errorInfo.type = 'quota_exceeded';
      errorInfo.message = 'OpenAI API quota exceeded';
      errorInfo.troubleshooting = [
        'Check your current usage at https://platform.openai.com/account/usage',
        'Wait until your quota resets (usually monthly)',
        'Consider upgrading your plan for higher limits',
        'Review your usage patterns to optimize API calls'
      ];
      errorInfo.canRetry = false;
    }
    // Check for content policy violations
    else if (error.message && error.message.includes('content_policy')) {
      errorInfo.type = 'content_policy';
      errorInfo.message = 'Content violates OpenAI usage policies';
      errorInfo.troubleshooting = [
        'Review your prompt content for policy violations',
        'Remove any potentially harmful or inappropriate content',
        'Check OpenAI usage policies at https://openai.com/policies/usage-policies',
        'Try rephrasing your prompt in a more appropriate way'
      ];
      errorInfo.canRetry = true; // Can retry with modified content
    }
    // Check for network/connectivity errors
    else if (error.code && (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT')) {
      errorInfo.type = 'network';
      errorInfo.message = 'Network connectivity issue';
      errorInfo.troubleshooting = [
        'Check your internet connection',
        'Verify firewall settings allow OpenAI API access',
        'Try again in a few minutes',
        'Check if OpenAI services are operational at https://status.openai.com'
      ];
      errorInfo.canRetry = true;
    }

    return errorInfo;
  }

  /**
   * Log an enhanced error with troubleshooting information
   * @param {Error} error - The original error
   * @param {Object} context - Additional context
   */
  static logEnhancedError(error, context = {}) {
    const errorInfo = this.parseOpenAIError(error, context);
    
    debugLogger.error(DEBUG_CATEGORIES.IMAGE_GENERATION, `${errorInfo.type.toUpperCase()}: ${errorInfo.message}`, {
      originalMessage: error.message,
      context: context,
      canRetry: errorInfo.canRetry,
      troubleshooting: errorInfo.troubleshooting
    });

    // Log troubleshooting steps
    if (errorInfo.troubleshooting.length > 0) {
      debugLogger.info(DEBUG_CATEGORIES.IMAGE_GENERATION, '🔧 Troubleshooting steps:', {
        steps: errorInfo.troubleshooting
      });
    }

    return errorInfo;
  }

  /**
   * Create a user-friendly error response for API endpoints
   * @param {Error} error - The original error
   * @param {Object} context - Additional context
   * @returns {Object} User-friendly error response
   */
  static createErrorResponse(error, context = {}) {
    const errorInfo = this.parseOpenAIError(error, context);
    
    return {
      success: false,
      error: errorInfo.message,
      type: errorInfo.type,
      canRetry: errorInfo.canRetry,
      troubleshooting: errorInfo.troubleshooting,
      context: {
        timestamp: new Date().toISOString(),
        ...context
      }
    };
  }

  /**
   * Check if an error should stop batch processing
   * @param {Error} error - The error to check
   * @returns {boolean} True if batch processing should stop
   */
  static shouldStopBatch(error) {
    const errorInfo = this.parseOpenAIError(error);
    
    // Stop batch processing for these error types
    const stopTypes = ['billing_limit', 'authentication', 'quota_exceeded'];
    return stopTypes.includes(errorInfo.type);
  }

  /**
   * Get recommended delay before retry based on error type
   * @param {Error} error - The error that occurred
   * @param {number} attemptNumber - Current attempt number
   * @returns {number} Delay in milliseconds
   */
  static getRetryDelay(error, attemptNumber = 1) {
    const errorInfo = this.parseOpenAIError(error);
    
    switch (errorInfo.type) {
      case 'rate_limit':
        return Math.min(60000 * attemptNumber, 300000); // 1-5 minutes
      case 'network':
        return Math.min(5000 * attemptNumber, 30000); // 5-30 seconds
      case 'content_policy':
        return 1000; // 1 second (user needs to fix content)
      default:
        return Math.min(10000 * attemptNumber, 60000); // 10-60 seconds
    }
  }

  /**
   * Create a comprehensive error report for debugging
   * @param {Error} error - The error to report
   * @param {Object} context - Additional context
   * @returns {Object} Detailed error report
   */
  static createErrorReport(error, context = {}) {
    const errorInfo = this.parseOpenAIError(error, context);
    
    return {
      timestamp: new Date().toISOString(),
      error: {
        type: errorInfo.type,
        message: errorInfo.message,
        originalMessage: error.message,
        stack: error.stack,
        name: error.name
      },
      context: context,
      troubleshooting: errorInfo.troubleshooting,
      canRetry: errorInfo.canRetry,
      recommendedAction: this.getRecommendedAction(errorInfo.type),
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        timestamp: Date.now()
      }
    };
  }

  /**
   * Get recommended action based on error type
   * @param {string} errorType - The type of error
   * @returns {string} Recommended action
   */
  static getRecommendedAction(errorType) {
    const actions = {
      billing_limit: 'Check and update your OpenAI billing settings',
      rate_limit: 'Wait and retry with longer delays between requests',
      authentication: 'Verify and update your OpenAI API key',
      quota_exceeded: 'Wait for quota reset or upgrade your plan',
      content_policy: 'Modify your prompt to comply with usage policies',
      network: 'Check your internet connection and retry',
      unknown: 'Check the error details and contact support if needed'
    };
    
    return actions[errorType] || actions.unknown;
  }
}

module.exports = ErrorHandler;
