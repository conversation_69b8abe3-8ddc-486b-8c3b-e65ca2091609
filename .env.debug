# Debug Configuration for AI Image Generator
# Copy this file to .env to enable debugging features

# ===========================================
# EXISTING CONFIGURATION (keep your values)
# ===========================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Cloudinary Configuration (optional)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Image Generation Settings
IMAGE_SIZE=1024x1024
IMAGE_QUALITY=medium
IMAGE_FORMAT=png
MODERATION_LEVEL=auto

# Scheduler Settings
SCHEDULER_ENABLED=true
SCHEDULER_CRON=0 */6 * * *
MAX_GENERATIONS_PER_DAY=10

# Server Settings
PORT=3000

# ===========================================
# NEW DEBUGGING CONFIGURATION
# ===========================================

# Global Debug Level
# Options: NONE, ERROR, WARN, INFO, DEBUG, VERBOSE
DEBUG_LEVEL=DEBUG

# Category-Specific Debug Levels
DEBUG_GENERAL=INFO
DEBUG_IMAGE_GEN=VERBOSE
DEBUG_BATCH=DEBUG
DEBUG_CLOUDINARY=INFO
DEBUG_SCHEDULER=DEBUG
DEBUG_API=INFO
DEBUG_FILES=WARN

# Debug Features
DEBUG_TIMESTAMPS=true
DEBUG_SHOW_CATEGORY=true
DEBUG_COLORS=true
DEBUG_LOG_FILE=false
DEBUG_LOG_PATH=./logs/debug.log

# ===========================================
# BATCH PROCESSING CONFIGURATION
# ===========================================

# Default batch processing settings
BATCH_DELAY_BETWEEN_ITEMS=2000
BATCH_MAX_RETRIES=1
BATCH_CONTINUE_ON_ERROR=true
BATCH_LOG_PROGRESS=true

# ===========================================
# DEBUGGING TIPS
# ===========================================

# For maximum debugging during development:
# DEBUG_LEVEL=VERBOSE
# DEBUG_IMAGE_GEN=VERBOSE
# DEBUG_BATCH=VERBOSE

# For production with minimal logging:
# DEBUG_LEVEL=ERROR
# DEBUG_IMAGE_GEN=WARN
# DEBUG_BATCH=INFO

# To enable file logging:
# DEBUG_LOG_FILE=true
# DEBUG_LOG_PATH=./logs/app.log

# To disable colors (useful for log files):
# DEBUG_COLORS=false
