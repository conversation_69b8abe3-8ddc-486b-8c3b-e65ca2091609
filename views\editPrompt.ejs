<div class="card" style="max-width: 600px; margin: 0 auto;">
  <div class="card-header">
    <h2 class="card-title">Edit Prompt</h2>
  </div>
  
  <form action="/edit/<%= prompt.id %>" method="POST" enctype="multipart/form-data">
    <div class="form-group">
      <label for="text" class="form-label">Prompt Text *</label>
      <textarea 
        id="text" 
        name="text" 
        class="form-textarea" 
        placeholder="Describe the image you want to generate..."
        required
        rows="4"
      ><%= prompt.text %></textarea>
      <small class="text-secondary">
        Be specific and descriptive. This text will be combined with the reference image to generate AI art.
      </small>
    </div>
    
    <div class="form-group">
      <label for="referenceImage" class="form-label">Reference Image</label>
      
      <% if (prompt.referenceImage) { %>
        <div class="mb-2">
          <div class="text-secondary mb-1">Current image:</div>
          <img src="/uploads/<%= prompt.referenceImage %>" alt="Current reference" 
               style="max-width: 200px; max-height: 150px; border-radius: 8px; object-fit: cover;">
        </div>
        
        <div class="form-checkbox mb-2">
          <input type="checkbox" id="removeImage" name="removeImage">
          <label for="removeImage" class="form-label mb-0">Remove current image</label>
        </div>
      <% } %>
      
      <div class="form-file">
        <input 
          type="file" 
          id="referenceImage" 
          name="referenceImage" 
          accept="image/*"
          onchange="previewImage(this)"
        >
        <div class="file-upload-text">
          <%= prompt.referenceImage ? 'Click to replace image or drag and drop' : 'Click to select an image or drag and drop' %>
        </div>
      </div>
      <small class="text-secondary">
        Upload a <%= prompt.referenceImage ? 'new' : '' %> reference image to inspire the AI generation. Supports JPG, PNG, WebP formats.
      </small>
    </div>
    
    <div id="image-preview" class="hidden" style="margin: 1rem 0;">
      <img id="preview-img" src="" alt="Preview" style="max-width: 100%; max-height: 200px; border-radius: 8px;">
    </div>
    
    <div class="form-group">
      <div class="form-checkbox">
        <input type="checkbox" id="enabled" name="enabled" <%= prompt.enabled ? 'checked' : '' %>>
        <label for="enabled" class="form-label mb-0">Enable this prompt for scheduled generation</label>
      </div>
      <small class="text-secondary">
        When enabled, this prompt will be included in the automatic generation cycle.
      </small>
    </div>
    
    <div class="form-group">
      <div class="text-secondary" style="font-size: 0.875rem;">
        <div>Created: <%= new Date(prompt.createdAt).toLocaleString() %></div>
        <% if (prompt.updatedAt) { %>
          <div>Last updated: <%= new Date(prompt.updatedAt).toLocaleString() %></div>
        <% } %>
        <div>Times used: <%= prompt.timesUsed || 0 %></div>
        <% if (prompt.lastUsed) { %>
          <div>Last used: <%= new Date(prompt.lastUsed).toLocaleString() %></div>
        <% } %>
      </div>
    </div>
    
    <div class="form-group flex gap-2">
      <button type="submit" class="btn btn-primary">
        💾 Update Prompt
      </button>
      <a href="/" class="btn btn-outline">
        Cancel
      </a>
      <button type="button" onclick="deletePrompt()" class="btn btn-danger">
        🗑️ Delete
      </button>
    </div>
  </form>
</div>

<script>
function previewImage(input) {
  const preview = document.getElementById('image-preview');
  const previewImg = document.getElementById('preview-img');
  const uploadText = document.querySelector('.file-upload-text');
  
  if (input.files && input.files[0]) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
      previewImg.src = e.target.result;
      preview.classList.remove('hidden');
      uploadText.textContent = input.files[0].name;
    };
    
    reader.readAsDataURL(input.files[0]);
  } else {
    preview.classList.add('hidden');
    uploadText.textContent = '<%= prompt.referenceImage ? "Click to replace image or drag and drop" : "Click to select an image or drag and drop" %>';
  }
}

function deletePrompt() {
  if (!confirm('Are you sure you want to delete this prompt? This action cannot be undone.')) {
    return;
  }
  
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = '/delete/<%= prompt.id %>';
  document.body.appendChild(form);
  form.submit();
}

// Drag and drop functionality
const fileInput = document.getElementById('referenceImage');
const fileUploadArea = document.querySelector('.form-file');

fileUploadArea.addEventListener('dragover', (e) => {
  e.preventDefault();
  fileUploadArea.style.borderColor = 'var(--primary-color)';
});

fileUploadArea.addEventListener('dragleave', (e) => {
  e.preventDefault();
  fileUploadArea.style.borderColor = 'var(--border-color)';
});

fileUploadArea.addEventListener('drop', (e) => {
  e.preventDefault();
  fileUploadArea.style.borderColor = 'var(--border-color)';
  
  const files = e.dataTransfer.files;
  if (files.length > 0 && files[0].type.startsWith('image/')) {
    fileInput.files = files;
    previewImage(fileInput);
  }
});

fileUploadArea.addEventListener('click', () => {
  fileInput.click();
});
</script>
