<div class="card">
  <div class="card-header">
    <h2 class="card-title">Generation History</h2>
    <div class="flex items-center gap-2">
      <span class="text-secondary">Total: <%= history.length %> generations</span>
    </div>
  </div>
  
  <% if (history.length === 0) { %>
    <div class="text-center">
      <p class="text-secondary mb-2">No generations yet</p>
      <a href="/add" class="btn btn-primary">Add your first prompt</a>
    </div>
  <% } else { %>
    <div class="grid gap-1">
      <% history.forEach(item => { %>
        <div class="history-item">
          <% if (item.cloudinaryUrl) { %>
            <img src="<%= item.cloudinaryUrl %>" alt="Generated" class="history-image">
          <% } else { %>
            <div class="history-image">🖼️</div>
          <% } %>
          
          <div class="history-content">
            <div class="history-prompt"><%= item.promptText %></div>
            <div class="history-meta">
              <div>
                Generated: <%= new Date(item.generatedAt).toLocaleString() %>
              </div>
              <div class="flex items-center gap-2 mt-1">
                <span class="badge <%= item.manual ? 'badge-info' : 'badge-success' %>">
                  <%= item.manual ? 'Manual' : 'Scheduled' %>
                </span>
                <% if (item.usage) { %>
                  <span class="text-secondary" style="font-size: 0.75rem;">
                    Tokens: <%= item.usage.input_tokens_details ? (item.usage.input_tokens_details.text_tokens + (item.usage.input_tokens_details.image_tokens || 0)) : 'N/A' %>
                  </span>
                <% } %>
              </div>
            </div>
          </div>
          
          <div class="flex gap-1" style="flex-direction: column;">
            <% if (item.cloudinaryUrl) { %>
              <a href="<%= item.cloudinaryUrl %>" target="_blank" class="btn btn-sm btn-primary">
                🔗 View Full
              </a>
            <% } %>
            <% if (item.generatedImage) { %>
              <a href="/generated-images/<%= item.generatedImage %>" target="_blank" class="btn btn-sm btn-outline">
                💾 Local File
              </a>
            <% } %>
            <button onclick="copyUrl('<%= item.cloudinaryUrl || '' %>')" class="btn btn-sm btn-outline">
              📋 Copy URL
            </button>
          </div>
        </div>
      <% }); %>
    </div>
    
    <!-- Pagination could be added here for large histories -->
    <% if (history.length >= 50) { %>
      <div class="text-center mt-2">
        <p class="text-secondary">Showing latest 50 generations</p>
      </div>
    <% } %>
  <% } %>
</div>

<!-- Generation Stats -->
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Statistics</h3>
  </div>
  
  <div class="stats">
    <div class="stat-card">
      <div class="stat-number"><%= settings.totalGenerations || 0 %></div>
      <div class="stat-label">Total Generations</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">
        <%= history.filter(h => new Date(h.generatedAt).toDateString() === new Date().toDateString()).length %>
      </div>
      <div class="stat-label">Generated Today</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">
        <%= history.filter(h => h.manual).length %>
      </div>
      <div class="stat-label">Manual Generations</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">
        <%= history.filter(h => h.scheduledGeneration).length %>
      </div>
      <div class="stat-label">Scheduled Generations</div>
    </div>
  </div>
  
  <% if (settings.lastGeneration) { %>
    <div class="text-center text-secondary">
      Last generation: <%= new Date(settings.lastGeneration).toLocaleString() %>
    </div>
  <% } %>
</div>

<script>
function copyUrl(url) {
  if (!url) {
    alert('No URL available to copy');
    return;
  }
  
  navigator.clipboard.writeText(url).then(() => {
    // Show temporary success message
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '✅ Copied!';
    button.style.background = 'var(--secondary-color)';
    
    setTimeout(() => {
      button.textContent = originalText;
      button.style.background = '';
    }, 2000);
  }).catch(err => {
    console.error('Failed to copy: ', err);
    alert('Failed to copy URL to clipboard');
  });
}
</script>
