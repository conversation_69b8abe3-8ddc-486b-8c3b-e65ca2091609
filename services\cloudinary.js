const cloudinary = require('cloudinary').v2;
const fs = require('fs-extra');

class CloudinaryService {
  constructor() {
    cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
    });
  }

  async uploadImage(localImagePath, metadata = {}) {
    try {
      console.log('☁️ Uploading image to Cloudinary:', localImagePath);
      
      const options = {
        folder: 'ai-generated',
        resource_type: 'image',
        use_filename: true,
        unique_filename: true,
        overwrite: false,
        tags: ['ai-generated', 'gpt-image-1'],
        context: {
          prompt: metadata.prompt || '',
          reference_image: metadata.referenceImage || '',
          generated_at: new Date().toISOString()
        }
      };

      const result = await cloudinary.uploader.upload(localImagePath, options);
      
      console.log('✅ Image uploaded to Cloudinary:', result.secure_url);
      
      return {
        url: result.secure_url,
        public_id: result.public_id,
        format: result.format,
        width: result.width,
        height: result.height,
        bytes: result.bytes,
        created_at: result.created_at,
        cloudinary_response: result
      };
    } catch (error) {
      console.error('❌ Cloudinary upload error:', error);
      throw error;
    }
  }

  async deleteImage(publicId) {
    try {
      console.log('🗑️ Deleting image from Cloudinary:', publicId);
      
      const result = await cloudinary.uploader.destroy(publicId);
      
      console.log('✅ Image deleted from Cloudinary:', result);
      return result;
    } catch (error) {
      console.error('❌ Cloudinary delete error:', error);
      throw error;
    }
  }

  async listImages(options = {}) {
    try {
      const defaultOptions = {
        type: 'upload',
        prefix: 'ai-generated/',
        max_results: 50,
        ...options
      };

      const result = await cloudinary.search
        .expression(`folder:ai-generated AND tags:ai-generated`)
        .sort_by([['created_at', 'desc']])
        .max_results(defaultOptions.max_results)
        .execute();

      return result.resources;
    } catch (error) {
      console.error('❌ Cloudinary list error:', error);
      throw error;
    }
  }

  async getImageInfo(publicId) {
    try {
      const result = await cloudinary.api.resource(publicId, {
        context: true,
        tags: true
      });
      
      return result;
    } catch (error) {
      console.error('❌ Cloudinary get info error:', error);
      throw error;
    }
  }

  // Generate optimized URLs
  generateOptimizedUrl(publicId, options = {}) {
    const defaultOptions = {
      quality: 'auto',
      format: 'auto',
      ...options
    };

    return cloudinary.url(publicId, defaultOptions);
  }

  // Generate thumbnail URL
  generateThumbnailUrl(publicId, width = 200, height = 200) {
    return cloudinary.url(publicId, {
      width: width,
      height: height,
      crop: 'fill',
      quality: 'auto',
      format: 'auto'
    });
  }
}

module.exports = new CloudinaryService();
