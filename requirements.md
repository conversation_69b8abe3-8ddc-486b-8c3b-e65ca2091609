
### 🧠 DEVELOPMENT PROMPT — FULL PROJECT SPECIFICATION

**Build a Node.js application that autonomously generates images using OpenAI’s image generation capabilities (gpt-image-1 via ChatGPT) using prompts from a list, where each prompt is paired with a reference image (via attached imaged) The generated image should be uploaded to Cloudinary. Include a web interface to manage prompts, add images, and see generated images.**

---

### 🔧 FEATURES & FUNCTIONAL REQUIREMENTS

#### 1. Scheduler (Backend)

* Sequentially pick a prompt from an active list.
* Each prompt includes:

  * Text prompt (string)
  * Image attached (used as visual reference)
* Combine the text + image into a structured prompt like:
  `"Use this reference image: [IMAGE_URL] — [PROMPT TEXT]"`

#### 2. Image Generation

* Use OpenAI’s API (gpt-image-1) to generate an image from the structured prompt.
* Receive and download generated image in a seperate folder.

#### 3. Cloudinary Upload

* Use Cloudinary Node SDK.
* Upload the image to a Cloudinary folder (e.g. `/ai-generated/`).
* Use timestamped filenames.
* Save metadata (prompt text, image URL, generation time, cloudinary URL) in a local database or JSON.

#### 4. Web Interface (Prompt Manager)

* Simple web dashboard using Express + EJS or plain HTML.
* List all prompts (text + image preview).
* Allow adding new prompts (text + image).
* Allow editing or deleting existing prompts.
* Toggle enable/disable per prompt.
* Show recent generation history (optional).

#### 5. Configurable Settings

* Prompt cycling mode: sequential
* Max generations per day (optional).

---

### 💾 Data Storage

* JSON file for:

  * List of prompts
  * Generation history (optional)

---

### ⚙️ Technologies to Use

* **Node.js**
* **Express.js**
* **Cloudinary SDK**
* **OpenAI API (gpt-image-1)**
* **EJS or plain HTML/Bootstrap** for frontend
* **SQLite** or local JSON

---

### 📁 Project Structure Suggestion

```
/ai-image-generator
├── server.js
├── routes/
│   └── prompts.js
├── views/
│   ├── index.ejs
│   └── addPrompt.ejs
├── public/
│   └── styles.css
├── data/
│   └── prompts.json
├── services/
│   ├── openai.js
│   ├── cloudinary.js
│   └── scheduler.js
├── .env
└── package.json
```

---

### 🧪 Testing

* Ensure image generation works with real prompt + image URL.
* Test Cloudinary upload.
* Validate UI CRUD operations.
* Validate that scheduler executes correctly.

---

### ✅ Completion Criteria

* Web interface allows prompt management.
* Scheduler runs on time and processes prompts.
* Images are generated and stored in Cloudinary with reference.
* Configurable via `.env` or UI.
* Logs or stores basic generation history.

