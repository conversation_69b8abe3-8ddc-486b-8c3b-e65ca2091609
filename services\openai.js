const OpenAI = require('openai');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { debugLogger, DEBUG_CATEGORIES } = require('../config/debug');

// Polyfill for Blob and File in Node.js
const { Blob } = require('buffer');

// File polyfill for Node.js
class File extends Blob {
  constructor(fileBits, fileName, options = {}) {
    super(fileBits, options);
    this.name = fileName;
    this.lastModified = Date.now();
  }
}

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async generateImage(prompt, referenceImagePath = null) {
    try {
      debugLogger.imageGeneration('🚀 Starting image generation...', {
        promptLength: prompt.length,
        referenceImage: referenceImagePath || 'none',
        model: 'gpt-image-1',
        size: process.env.IMAGE_SIZE || '1024x1024',
        quality: process.env.IMAGE_QUALITY || 'medium'
      });

      let response;
      let usedReferenceImage = false;

      if (referenceImagePath && await fs.pathExists(referenceImagePath)) {
        debugLogger.imageGeneration('📸 Using reference image with edit endpoint', {
          path: referenceImagePath
        });

        try {
          const imageBuffer = await this.resizeImageForOpenAI(await fs.readFile(referenceImagePath));
          const mimeType = this.getMimeType(referenceImagePath);
          const imageFile = new File([imageBuffer], `reference.${mimeType.split('/')[1]}`, { type: mimeType });

          debugLogger.verbose(DEBUG_CATEGORIES.IMAGE_GENERATION, 'Reference image processed', {
            fileSize: imageBuffer.length,
            mimeType: mimeType
          });

          response = await this.client.images.edit({
              model: 'gpt-image-1',
              image: imageFile,
              prompt: prompt,
              n: 1,
              size: process.env.IMAGE_SIZE || '1024x1024',
              quality: process.env.IMAGE_QUALITY || 'high',
          });

          usedReferenceImage = true;
          debugLogger.imageGeneration('✅ Reference image processed successfully');

        } catch (referenceError) {
          debugLogger.error(DEBUG_CATEGORIES.IMAGE_GENERATION, 'Error processing reference image', {
            error: referenceError.message,
            path: referenceImagePath
          });
          debugLogger.imageGeneration('🔄 Falling back to text-only generation');

          // Fall back to text-only generation
          const requestBody = {
              model: 'gpt-image-1',
              prompt: prompt,
              n: 1,
              size: process.env.IMAGE_SIZE || '1024x1024',
              quality: process.env.IMAGE_QUALITY || 'medium',
              moderation: process.env.MODERATION_LEVEL || 'auto',
              output_format: process.env.IMAGE_FORMAT || 'png'
          };
          response = await this.client.images.generate(requestBody);
        }
      } else {
          // Standard text-only generation
          debugLogger.imageGeneration('📝 Using text-only generation');
          const requestBody = {
              model: 'gpt-image-1',
              prompt: prompt,
              n: 1,
              size: process.env.IMAGE_SIZE || '1024x1024',
              quality: process.env.IMAGE_QUALITY || 'medium',
              moderation: process.env.MODERATION_LEVEL || 'auto',
              output_format: process.env.IMAGE_FORMAT || 'png'
          };
          debugLogger.verbose(DEBUG_CATEGORIES.IMAGE_GENERATION, 'Request body', requestBody);
          response = await this.client.images.generate(requestBody);
      }

      debugLogger.imageGeneration('📡 Received response from OpenAI API');

      if (response.data && response.data.length > 0) {
        const imageData = response.data[0];
        const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
        const filepath = path.join(__dirname, '..', 'generated-images', filename);

        debugLogger.imageGeneration('💾 Saving image', {
          filename: filename,
          filepath: filepath,
          imageDataSize: imageData.b64_json ? imageData.b64_json.length : 'unknown'
        });

        // Ensure the generated-images directory exists
        await fs.ensureDir(path.join(__dirname, '..', 'generated-images'));

        // Decode base64 and save image
        const buffer = Buffer.from(imageData.b64_json, 'base64');
        await fs.writeFile(filepath, buffer);

        debugLogger.imageGeneration('✅ Image generated and saved successfully', {
          finalFileSize: buffer.length,
          filename: filename
        });

        return {
          success: true,
          filename: filename,
          filepath: filepath,
          usage: response.usage || null,
          usedReferenceImage: usedReferenceImage
        };
      } else {
        debugLogger.error(DEBUG_CATEGORIES.IMAGE_GENERATION, 'No image data received from OpenAI API', {
          responseStructure: response
        });
        throw new Error('No image data received from OpenAI');
      }
    } catch (error) {
      debugLogger.logError(DEBUG_CATEGORIES.IMAGE_GENERATION, error, {
        prompt: prompt.substring(0, 100),
        referenceImagePath: referenceImagePath,
        usedReferenceImage: usedReferenceImage
      });

      // Log additional error details if available
      if (error.response) {
        debugLogger.error(DEBUG_CATEGORIES.IMAGE_GENERATION, 'API Response Error', {
          status: error.response.status,
          data: error.response.data
        });
      }

      throw error;
    }
  }


  async generateImageFromPromptData(promptData) {
    try {
      debugLogger.imageGeneration('🎯 Generating image from prompt data', {
        textPreview: promptData.text.substring(0, 200) + (promptData.text.length > 200 ? '...' : ''),
        hasReferenceImage: !!promptData.referenceImage,
        referenceImage: promptData.referenceImage || 'none',
        enabled: promptData.enabled !== false
      });

      // Check if prompt is enabled (default to true if not specified)
      if (promptData.enabled === false) {
        debugLogger.imageGeneration('⏭️ Prompt is disabled, skipping generation');
        return {
          success: false,
          reason: 'Prompt is disabled'
        };
      }

      let enhancedPrompt = promptData.text;

      const referenceImagePath = promptData.referenceImage ? path.join(__dirname, '..', 'uploads', promptData.referenceImage) : null;

      // Validate reference image if specified
      if (referenceImagePath) {
        const fs = require('fs-extra');
        if (!await fs.pathExists(referenceImagePath)) {
          debugLogger.warn(DEBUG_CATEGORIES.IMAGE_GENERATION, 'Reference image not found', {
            path: referenceImagePath,
            action: 'Proceeding with text-only generation'
          });
        }
      }

      const startTime = Date.now();
      const result = await this.generateImage(enhancedPrompt, referenceImagePath);
      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(2);

      if (result.success) {
        debugLogger.imageGeneration(`✅ Image generation completed in ${duration}s`, {
          filename: result.filename,
          filepath: result.filepath,
          usage: result.usage,
          duration: duration
        });
      } else {
        debugLogger.error(DEBUG_CATEGORIES.IMAGE_GENERATION, `Image generation failed after ${duration}s`, {
          duration: duration,
          result: result
        });
      }

      return result;
    } catch (error) {
      debugLogger.logError(DEBUG_CATEGORIES.IMAGE_GENERATION, error, {
        promptData: {
          textPreview: promptData.text?.substring(0, 100),
          referenceImage: promptData.referenceImage,
          enabled: promptData.enabled
        }
      });
      throw error;
    }
  }

  // Helper method to get MIME type from file extension
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.gif': 'image/gif'
    };
    return mimeTypes[ext] || 'image/jpeg';
  }

  // Helper method to resize image for OpenAI API compatibility
  async resizeImageForOpenAI(imageBuffer) {
    try {
      // Try to use sharp for high-quality resizing
      const sharp = require('sharp');

      // Resize to a smaller dimension that should result in <16KB
      const resizedBuffer = await sharp(imageBuffer)
        .resize(256, 256, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality: 60 }) // Use JPEG with lower quality to reduce size
        .toBuffer();

      console.log('✅ Image resized using Sharp');
      return resizedBuffer;
    } catch (sharpError) {
      console.log('⚠️ Sharp not available, using fallback approach');

      // Fallback: Just truncate the buffer (not ideal but will work)
      // This is a very basic approach - in production you'd want proper image resizing
      const maxSize = 15000; // Leave some buffer under 16KB
      if (imageBuffer.length > maxSize) {
        console.log('⚠️ Using buffer truncation fallback (not ideal)');
        return imageBuffer.slice(0, maxSize);
      }

      return imageBuffer;
    }
  }
}

module.exports = new OpenAIService();
